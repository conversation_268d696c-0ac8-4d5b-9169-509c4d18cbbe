#!/usr/bin/env python3
"""
Simple LightRAG Agent with AWS Bedrock and Google ADK
Single entry point for document Q&A system

Usage:
    python main.py
"""
import asyncio
import logging
import sys
from pathlib import Path

from config import DOCS_PATH, validate_bedrock_access
from lightrag_bedrock_fixed import FixedLightRAGBedrock

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleLightRAGApp:
    """Main application class"""
    
    def __init__(self):
        self.agent = FixedLightRAGBedrock()
        self.logger = logging.getLogger(__name__)
    
    async def setup(self):
        """Setup the application"""
        try:
            self.logger.info("=== Simple LightRAG Agent with AWS Bedrock ===")
            
            # Validate Bedrock access
            self.logger.info("Validating AWS Bedrock access...")
            if not validate_bedrock_access():
                raise Exception("AWS Bedrock access validation failed")
            self.logger.info("✅ AWS Bedrock access validated")
            
            # Initialize agent
            self.logger.info("Initializing LightRAG agent...")
            success = await self.agent.initialize()
            if not success:
                raise Exception("Failed to initialize agent")
            self.logger.info("✅ LightRAG agent initialized")
            
            # Load documents
            self.logger.info(f"Loading documents from {DOCS_PATH}...")
            success = await self.agent.ingest_documents(DOCS_PATH)
            if not success:
                raise Exception("Failed to load documents")
            self.logger.info("✅ Documents loaded successfully")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Setup failed: {e}")
            return False
    
    async def interactive_mode(self):
        """Run interactive Q&A mode"""
        self.logger.info("\n=== Interactive Q&A Mode ===")
        self.logger.info("Ask questions about the documents. Type 'quit' to exit.")
        
        while True:
            try:
                # Get user input
                question = input("\n🤔 Your question: ").strip()
                
                if question.lower() in ['quit', 'exit', 'q']:
                    self.logger.info("Goodbye!")
                    break
                
                if not question:
                    continue
                
                # Process query
                self.logger.info(f"Processing: {question}")
                response = await self.agent.query(question, mode="hybrid")

                # Display response
                print(f"\n🤖 Response:")
                print("-" * 50)
                print(response)
                print("-" * 50)
                
            except KeyboardInterrupt:
                self.logger.info("\nGoodbye!")
                break
            except Exception as e:
                self.logger.error(f"Error processing question: {e}")
    
    async def demo_queries(self):
        """Run some demo queries"""
        demo_questions = [
            "What is Dropi?",
            "What are the main features of Dropi Academy?",
            "How does the tutorial work?",
            "What information is available about Dropi?"
        ]
        
        self.logger.info("\n=== Demo Queries ===")
        
        for question in demo_questions:
            try:
                self.logger.info(f"\n🤔 Demo Question: {question}")
                response = await self.agent.query(question, mode="hybrid")
                
                print(f"\n🤖 Response:")
                print("-" * 50)
                print(response)
                print("-" * 50)
                
                # Small delay between queries
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in demo query: {e}")

async def main():
    """Main function"""
    app = SimpleLightRAGApp()
    
    # Setup
    success = await app.setup()
    if not success:
        logger.error("Failed to setup application")
        sys.exit(1)
    
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        # Run demo mode
        await app.demo_queries()
    else:
        # Run interactive mode
        await app.interactive_mode()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)
