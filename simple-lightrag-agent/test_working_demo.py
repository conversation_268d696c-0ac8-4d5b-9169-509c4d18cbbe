#!/usr/bin/env python3
"""
Working demo using the successful patterns
"""
import asyncio
import logging
import os
from pathlib import Path

# Setup environment first
os.environ["AWS_PROFILE"] = "IA"
os.environ["AWS_DEFAULT_REGION"] = "us-east-2"
os.environ["AWS_ACCESS_KEY_ID"] = ""
os.environ["AWS_SECRET_ACCESS_KEY"] = ""
os.environ["AWS_SESSION_TOKEN"] = ""

from lightrag import LightRAG, QueryParam
from lightrag.llm.bedrock import bedrock_complete_if_cache, bedrock_embed
from lightrag.utils import EmbeddingFunc
from lightrag.kg.shared_storage import initialize_pipeline_status
from document_processor import DocxProcessor

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Configuration
LLM_MODEL = "us.amazon.nova-micro-v1:0"
EMBEDDING_MODEL = "amazon.titan-embed-text-v2:0"
DOCS_PATH = "/home/<USER>/dropi-ubuntu/agent-v2/docs"
WORKING_DIR = Path("./lightrag_data")

async def create_bedrock_llm_func(prompt, **kwargs):
    """Custom LLM function for Bedrock"""
    # Remove conflicting parameters
    kwargs.pop('model', None)
    kwargs.pop('hashing_kv', None)

    try:
        logger.debug(f"LLM call with prompt length: {len(prompt)}, kwargs: {kwargs}")
        result = await bedrock_complete_if_cache(
            model=LLM_MODEL,
            prompt=prompt,
            **kwargs
        )
        logger.debug(f"LLM response length: {len(result)}")
        return result
    except Exception as e:
        logger.error(f"Detailed Bedrock LLM error: {type(e).__name__}: {e}")
        # Try to get more details from the exception
        if hasattr(e, '__cause__') and e.__cause__:
            logger.error(f"Caused by: {type(e.__cause__).__name__}: {e.__cause__}")
        raise

async def create_bedrock_embedding_func(texts):
    """Custom embedding function for Bedrock"""
    return await bedrock_embed(
        texts=texts,
        model=EMBEDDING_MODEL
    )

async def main():
    """Main demo function"""
    try:
        logger.info("=== Working LightRAG Demo with Bedrock ===")
        
        # Create working directory
        WORKING_DIR.mkdir(exist_ok=True)
        
        # Initialize LightRAG
        logger.info("Initializing LightRAG...")
        rag = LightRAG(
            working_dir=str(WORKING_DIR),
            llm_model_func=create_bedrock_llm_func,
            embedding_func=EmbeddingFunc(
                embedding_dim=1024,
                max_token_size=8192,
                func=create_bedrock_embedding_func
            )
        )
        
        # Initialize storages
        await rag.initialize_storages()
        await initialize_pipeline_status()
        logger.info("✅ LightRAG initialized")
        
        # Load documents
        logger.info("Loading .docx documents...")
        processor = DocxProcessor(DOCS_PATH)
        combined_content = processor.get_combined_content()
        
        if combined_content.strip():
            logger.info(f"Loaded {len(combined_content)} characters from documents")
            
            # Insert documents
            logger.info("Inserting documents into LightRAG...")
            await rag.ainsert(combined_content)
            logger.info("✅ Documents inserted successfully")
            
            # Test queries
            test_questions = [
                "What is Dropi?",
                "What are the main features of Dropi Academy?",
                "How does the tutorial work?"
            ]
            
            for question in test_questions:
                logger.info(f"\n🤔 Question: {question}")
                try:
                    # Try naive mode first (simpler)
                    response = await rag.aquery(question, param=QueryParam(mode="naive"))
                    print(f"\n🤖 Response:")
                    print("-" * 50)
                    print(response)
                    print("-" * 50)
                except Exception as e:
                    logger.error(f"Query failed: {e}")
                    print(f"\n❌ Error: {e}")
                
                await asyncio.sleep(1)  # Small delay between queries
        else:
            logger.error("No content found in documents")
            
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise
    finally:
        # Cleanup
        if 'rag' in locals():
            await rag.finalize_storages()

if __name__ == "__main__":
    asyncio.run(main())
