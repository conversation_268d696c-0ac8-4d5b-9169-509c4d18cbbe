#!/usr/bin/env python3
"""
Simple test script to verify Bedrock connectivity
"""
import asyncio
import logging
import os
from lightrag.llm.bedrock import bedrock_complete_if_cache, bedrock_embed
from config import LLM_MODEL, EMBEDDING_MODEL, AWS_PROFILE, AWS_REGION

# Setup environment
os.environ["AWS_PROFILE"] = AWS_PROFILE
os.environ["AWS_DEFAULT_REGION"] = AWS_REGION
os.environ["AWS_ACCESS_KEY_ID"] = ""
os.environ["AWS_SECRET_ACCESS_KEY"] = ""
os.environ["AWS_SESSION_TOKEN"] = ""

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_bedrock_llm():
    """Test Bedrock LLM"""
    try:
        logger.info(f"Testing Bedrock LLM with model: {LLM_MODEL}")
        response = await bedrock_complete_if_cache(
            model=LLM_MODEL,
            prompt="What is artificial intelligence? Please provide a brief answer.",
        )
        logger.info(f"LLM Response: {response}")
        return True
    except Exception as e:
        logger.error(f"LLM Test failed: {e}")
        return False

async def test_bedrock_embedding():
    """Test Bedrock Embedding"""
    try:
        logger.info(f"Testing Bedrock Embedding with model: {EMBEDDING_MODEL}")
        embeddings = await bedrock_embed(
            texts=["This is a test sentence for embedding."],
            model=EMBEDDING_MODEL
        )
        logger.info(f"Embedding shape: {embeddings.shape}")
        return True
    except Exception as e:
        logger.error(f"Embedding Test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("=== Bedrock Connectivity Test ===")
    
    # Test LLM
    llm_success = await test_bedrock_llm()
    
    # Test Embedding
    embedding_success = await test_bedrock_embedding()
    
    if llm_success and embedding_success:
        logger.info("✅ All Bedrock tests passed!")
    else:
        logger.error("❌ Some Bedrock tests failed!")

if __name__ == "__main__":
    asyncio.run(main())
