#!/usr/bin/env python3
"""
Direct test of Bedrock API calls to isolate the authentication issue
"""
import asyncio
import logging
import os
import boto3
import json

# Setup environment
os.environ["AWS_PROFILE"] = "IA"
os.environ["AWS_DEFAULT_REGION"] = "us-east-2"

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_bedrock_direct():
    """Test Bedrock API directly using boto3"""
    try:
        logger.info("=== Direct Bedrock API Test ===")
        
        # Create Bedrock client
        session = boto3.Session(profile_name="IA")
        client = session.client('bedrock-runtime', region_name='us-east-2')
        
        # Test Nova Micro LLM
        logger.info("Testing Nova Micro LLM...")
        
        prompt = "What is artificial intelligence? Please provide a brief answer."
        
        request_body = {
            "inputText": prompt,
            "textGenerationConfig": {
                "maxTokenCount": 100,
                "temperature": 0.1,
                "topP": 0.9
            }
        }
        
        response = client.invoke_model(
            modelId="amazon.nova-micro-v1:0",
            body=json.dumps(request_body),
            contentType="application/json",
            accept="application/json"
        )
        
        response_body = json.loads(response['body'].read())
        logger.info(f"✅ Nova Micro Response: {response_body.get('outputText', 'No output')}")
        
        # Test Titan Embeddings
        logger.info("Testing Titan Embeddings...")
        
        embed_request = {
            "inputText": "This is a test sentence for embedding."
        }
        
        embed_response = client.invoke_model(
            modelId="amazon.titan-embed-text-v2:0",
            body=json.dumps(embed_request),
            contentType="application/json",
            accept="application/json"
        )
        
        embed_body = json.loads(embed_response['body'].read())
        embedding = embed_body.get('embedding', [])
        logger.info(f"✅ Titan Embedding: {len(embedding)} dimensions")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Direct Bedrock test failed: {e}")
        return False

async def test_lightrag_bedrock_functions():
    """Test LightRAG's Bedrock functions directly"""
    try:
        logger.info("\n=== LightRAG Bedrock Functions Test ===")
        
        from lightrag.llm.bedrock import bedrock_complete_if_cache, bedrock_embed
        
        # Test LLM function
        logger.info("Testing LightRAG bedrock_complete_if_cache...")
        
        llm_response = await bedrock_complete_if_cache(
            model="amazon.nova-micro-v1:0",
            prompt="What is artificial intelligence? Please provide a brief answer."
        )
        
        logger.info(f"✅ LightRAG LLM Response: {llm_response}")
        
        # Test embedding function
        logger.info("Testing LightRAG bedrock_embed...")
        
        embeddings = await bedrock_embed(
            texts=["This is a test sentence for embedding."],
            model="amazon.titan-embed-text-v2:0"
        )
        
        logger.info(f"✅ LightRAG Embeddings: {embeddings.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LightRAG Bedrock functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_with_environment_variables():
    """Test with different environment variable configurations"""
    try:
        logger.info("\n=== Environment Variables Test ===")
        
        # Clear any existing AWS env vars
        for key in ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_SESSION_TOKEN']:
            if key in os.environ:
                del os.environ[key]
        
        # Set profile and region
        os.environ["AWS_PROFILE"] = "IA"
        os.environ["AWS_DEFAULT_REGION"] = "us-east-2"
        
        logger.info("Environment variables set:")
        logger.info(f"  AWS_PROFILE: {os.environ.get('AWS_PROFILE')}")
        logger.info(f"  AWS_DEFAULT_REGION: {os.environ.get('AWS_DEFAULT_REGION')}")
        
        from lightrag.llm.bedrock import bedrock_complete_if_cache
        
        response = await bedrock_complete_if_cache(
            model="amazon.nova-micro-v1:0",
            prompt="Hello, this is a test."
        )
        
        logger.info(f"✅ Environment test successful: {response}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Environment test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    logger.info("🔍 Diagnosing Bedrock Authentication Issue")
    logger.info("=" * 50)
    
    # Test 1: Direct Bedrock API
    direct_success = await test_bedrock_direct()
    
    # Test 2: LightRAG Bedrock functions
    lightrag_success = await test_lightrag_bedrock_functions()
    
    # Test 3: Environment variables
    env_success = await test_with_environment_variables()
    
    logger.info("\n" + "=" * 50)
    logger.info("🔍 Diagnosis Results:")
    logger.info(f"  Direct Bedrock API: {'✅ PASS' if direct_success else '❌ FAIL'}")
    logger.info(f"  LightRAG Functions: {'✅ PASS' if lightrag_success else '❌ FAIL'}")
    logger.info(f"  Environment Test: {'✅ PASS' if env_success else '❌ FAIL'}")
    
    if direct_success and not lightrag_success:
        logger.info("\n💡 Issue identified: LightRAG Bedrock integration problem")
    elif not direct_success:
        logger.info("\n💡 Issue identified: AWS Bedrock authentication problem")
    else:
        logger.info("\n✅ All tests passed - authentication is working!")

if __name__ == "__main__":
    asyncio.run(main())
