"""
Configuration for simplified LightRAG agent with AWS Bedrock
"""
import os
import boto3
from pathlib import Path

# AWS Bedrock Configuration (using existing validated setup)
AWS_PROFILE = "IA"
AWS_REGION = "us-east-2"

# Validated Bedrock Models
LLM_MODEL = "us.amazon.nova-micro-v1:0"  # Nova Micro for LLM
EMBEDDING_MODEL = "amazon.titan-embed-text-v2:0"  # Titan V2 for embeddings

# LLM Configuration
LLM_CONFIG = {
    "provider": "bedrock",
    "model": LLM_MODEL,
    "format": "Nova",
    "region": AWS_REGION,
    "profile": AWS_PROFILE,
    "temperature": 0.1,
    "max_tokens": 4000,
    "top_p": 0.9,
}

# Embedding Configuration
EMBEDDING_CONFIG = {
    "provider": "bedrock",
    "model": EMBEDDING_MODEL,
    "format": "Titan Embed",
    "region": AWS_REGION,
    "profile": AWS_PROFILE,
    "dimensions": 1024,  # Titan V2 uses 1024 dimensions
    "normalize": True,
}

# Document Configuration
DOCS_PATH = "/home/<USER>/dropi-ubuntu/agent-v2/docs"
SUPPORTED_EXTENSIONS = [".docx"]

# Working Directory for LightRAG
WORKING_DIR = Path("./lightrag_data")

def get_bedrock_client():
    """Initialize and return Bedrock client"""
    try:
        session = boto3.Session(profile_name=AWS_PROFILE)
        client = session.client('bedrock-runtime', region_name=AWS_REGION)
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Bedrock client: {e}")

def validate_bedrock_access():
    """Validate access to Bedrock models"""
    try:
        client = get_bedrock_client()
        # Simple validation - just check if client is created successfully
        return True
    except Exception as e:
        print(f"Bedrock access validation failed: {e}")
        return False
