"""
LightRAG integration with AWS Bedrock
"""
import os
import asyncio
import logging
from pathlib import Path
from lightrag import LightRAG, QueryParam
from lightrag.llm.bedrock import bedrock_complete, bedrock_embed
from lightrag.utils import EmbeddingFunc
from lightrag.kg.shared_storage import initialize_pipeline_status
import nest_asyncio

from config import (
    LLM_CONFIG, EMBEDDING_CONFIG, WORKING_DIR, 
    AWS_PROFILE, AWS_REGION, LLM_MODEL, EMBEDDING_MODEL
)
from document_processor import DocxProcessor

# Apply nest_asyncio for Jupyter/async compatibility
nest_asyncio.apply()

logger = logging.getLogger(__name__)

class LightRAGBedrock:
    """LightRAG implementation using AWS Bedrock models"""
    
    def __init__(self, working_dir: Path = WORKING_DIR):
        self.working_dir = working_dir
        self.working_dir.mkdir(exist_ok=True)
        self.rag = None
        self.logger = logging.getLogger(__name__)
        
        # Set AWS environment for LightRAG Bedrock integration
        self._setup_aws_environment()
    
    def _setup_aws_environment(self):
        """Setup AWS environment variables for Bedrock"""
        # LightRAG Bedrock integration uses these environment variables
        os.environ["AWS_PROFILE"] = AWS_PROFILE
        os.environ["AWS_DEFAULT_REGION"] = AWS_REGION

        # Set AWS credentials to empty strings to avoid None type errors
        # The profile will be used for authentication
        if "AWS_ACCESS_KEY_ID" not in os.environ:
            os.environ["AWS_ACCESS_KEY_ID"] = ""
        if "AWS_SECRET_ACCESS_KEY" not in os.environ:
            os.environ["AWS_SECRET_ACCESS_KEY"] = ""
        if "AWS_SESSION_TOKEN" not in os.environ:
            os.environ["AWS_SESSION_TOKEN"] = ""

        self.logger.info(f"AWS environment configured: Profile={AWS_PROFILE}, Region={AWS_REGION}")
    
    async def initialize(self):
        """Initialize LightRAG with Bedrock models"""
        try:
            self.logger.info("Initializing LightRAG with Bedrock models...")
            
            # Create custom wrapper functions for Bedrock
            async def bedrock_llm_func(prompt, **kwargs):
                # Remove model from kwargs if present to avoid conflict
                kwargs.pop('model', None)
                # Log parameters for debugging
                self.logger.debug(f"LLM call with prompt length: {len(prompt)}, kwargs: {list(kwargs.keys())}")
                # Call bedrock_complete_if_cache directly to avoid parameter conflicts
                from lightrag.llm.bedrock import bedrock_complete_if_cache
                try:
                    return await bedrock_complete_if_cache(
                        model=LLM_MODEL,
                        prompt=prompt,
                        **kwargs
                    )
                except Exception as e:
                    self.logger.error(f"Bedrock LLM call failed: {e}")
                    raise

            async def bedrock_embedding_func(texts):
                return await bedrock_embed(
                    texts=texts,
                    model=EMBEDDING_MODEL
                )

            # Create LightRAG instance with Bedrock
            self.rag = LightRAG(
                working_dir=str(self.working_dir),
                llm_model_func=bedrock_llm_func,
                embedding_func=EmbeddingFunc(
                    embedding_dim=EMBEDDING_CONFIG["dimensions"],
                    max_token_size=8192,
                    func=bedrock_embedding_func
                )
            )
            
            # Initialize storages and pipeline
            await self.rag.initialize_storages()
            await initialize_pipeline_status()
            
            self.logger.info("LightRAG initialized successfully with Bedrock")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LightRAG: {e}")
            return False
    
    async def ingest_documents(self, docs_path: str):
        """Ingest .docx documents into LightRAG"""
        try:
            if not self.rag:
                raise Exception("LightRAG not initialized. Call initialize() first.")
            
            self.logger.info(f"Starting document ingestion from {docs_path}")
            
            # Process documents
            processor = DocxProcessor(docs_path)
            combined_content = processor.get_combined_content()
            
            if not combined_content.strip():
                raise Exception("No content found in documents")
            
            # Insert content into LightRAG
            await self.rag.ainsert(combined_content)
            
            self.logger.info("Document ingestion completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Document ingestion failed: {e}")
            return False
    
    async def query(self, question: str, mode: str = "hybrid") -> str:
        """Query the RAG system"""
        try:
            if not self.rag:
                raise Exception("LightRAG not initialized. Call initialize() first.")
            
            self.logger.info(f"Processing query: {question} (mode: {mode})")
            
            # Execute query
            response = await self.rag.aquery(
                question, 
                param=QueryParam(mode=mode)
            )
            
            self.logger.info("Query processed successfully")
            return response
            
        except Exception as e:
            self.logger.error(f"Query failed: {e}")
            return f"Error processing query: {e}"
