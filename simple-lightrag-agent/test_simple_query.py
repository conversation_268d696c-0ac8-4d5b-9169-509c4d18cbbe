#!/usr/bin/env python3
"""
Test simple query with naive mode
"""
import asyncio
import logging
from adk_agent import SimpleLightRAGAgent

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_simple_query():
    """Test a simple query using naive mode"""
    try:
        logger.info("Initializing SimpleLightRAGAgent...")
        agent = SimpleLightRAGAgent()
        
        # Initialize
        success = await agent.initialize()
        if not success:
            logger.error("Failed to initialize agent")
            return False
        
        logger.info("Agent initialized successfully")
        
        # Load documents
        logger.info("Loading documents...")
        success = await agent.load_documents()
        if not success:
            logger.error("Failed to load documents")
            return False
        
        logger.info("Documents loaded successfully")
        
        # Test naive query (simpler, doesn't use knowledge graph)
        logger.info("Testing naive query...")
        response = await agent.query("What is Dropi?", mode="naive")
        logger.info(f"Response: {response}")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("=== Simple Query Test ===")
    success = await test_simple_query()
    
    if success:
        logger.info("✅ Simple query test passed!")
    else:
        logger.error("❌ Simple query test failed!")

if __name__ == "__main__":
    asyncio.run(main())
