#!/usr/bin/env python3
"""
Test RAG with proper context retrieval using hybrid mode
"""
import asyncio
import logging
from lightrag_bedrock_fixed import FixedLightRAGBedrock
from config import DOCS_PATH

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_rag_with_context():
    """Test RAG with proper context retrieval"""
    try:
        logger.info("=== Testing RAG with Document Context ===")
        
        # Initialize fixed LightRAG
        rag = FixedLightRAGBedrock()
        
        logger.info("1. Initializing LightRAG...")
        success = await rag.initialize()
        if not success:
            logger.error("❌ Failed to initialize LightRAG")
            return False
        logger.info("✅ LightRAG initialized successfully")
        
        # Test questions that should find content in the documents
        test_questions = [
            "What is Dropi and what does it do?",
            "Tell me about Dropi Academy and its features",
            "What countries does Dropi operate in?",
            "What types of users does Dropi serve?",
            "What courses are available in Dropi Academy?"
        ]
        
        # Test with hybrid mode (combines vector search + knowledge graph)
        logger.info("2. Testing with hybrid mode (vector + knowledge graph)...")
        for i, question in enumerate(test_questions):
            logger.info(f"\n🤔 Question {i+1}: {question}")
            try:
                response = await rag.query(question, mode="hybrid")
                print(f"\n🤖 Hybrid Mode Response:")
                print("-" * 60)
                print(response)
                print("-" * 60)
                
                # Check if response contains document-specific content
                dropi_keywords = ["Dropi", "Colombia", "ecommerce", "logistics", "Academy", "dropshipper"]
                found_keywords = [kw for kw in dropi_keywords if kw.lower() in response.lower()]
                
                if found_keywords:
                    logger.info(f"✅ Document context found! Keywords: {found_keywords}")
                else:
                    logger.warning("⚠️ Response seems generic - may not be using document context")
                
            except Exception as e:
                logger.error(f"❌ Query failed: {e}")
            
            await asyncio.sleep(2)  # Delay between queries
        
        # Test with global mode (knowledge graph focused)
        logger.info("\n3. Testing with global mode (knowledge graph focused)...")
        try:
            response = await rag.query("What is Dropi and what services does it provide?", mode="global")
            print(f"\n🤖 Global Mode Response:")
            print("-" * 60)
            print(response)
            print("-" * 60)
            
            # Check for document-specific content
            if "Colombia" in response or "ecommerce" in response or "logistics" in response:
                logger.info("✅ Global mode using document context!")
            else:
                logger.warning("⚠️ Global mode response seems generic")
                
        except Exception as e:
            logger.error(f"❌ Global mode query failed: {e}")
        
        # Test with local mode (entity-focused)
        logger.info("\n4. Testing with local mode (entity-focused)...")
        try:
            response = await rag.query("Tell me about Dropi Academy", mode="local")
            print(f"\n🤖 Local Mode Response:")
            print("-" * 60)
            print(response)
            print("-" * 60)
            
            # Check for document-specific content
            if "Academy" in response and ("course" in response.lower() or "education" in response.lower()):
                logger.info("✅ Local mode using document context!")
            else:
                logger.warning("⚠️ Local mode response seems generic")
                
        except Exception as e:
            logger.error(f"❌ Local mode query failed: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ RAG context test failed: {e}")
        return False

async def test_vector_search_directly():
    """Test vector search functionality directly"""
    try:
        logger.info("\n=== Testing Vector Search Directly ===")
        
        # Check if we can access the vector databases
        import json
        from pathlib import Path
        
        data_dir = Path("lightrag_data")
        
        # Load entities
        entities_file = data_dir / "vdb_entities.json"
        if entities_file.exists():
            with open(entities_file, 'r') as f:
                entities_data = json.load(f)
            
            logger.info(f"✅ Found {len(entities_data['data'])} entities in vector DB")
            
            # Show some entities that should match "Dropi"
            dropi_entities = [e for e in entities_data['data'] if 'dropi' in e.get('entity_name', '').lower()]
            logger.info(f"✅ Found {len(dropi_entities)} Dropi-related entities")
            
            for entity in dropi_entities[:3]:
                logger.info(f"  • {entity['entity_name']}: {entity['content'][:100]}...")
        
        # Load chunks
        chunks_file = data_dir / "kv_store_text_chunks.json"
        if chunks_file.exists():
            with open(chunks_file, 'r') as f:
                chunks_data = json.load(f)
            
            logger.info(f"✅ Found {len(chunks_data)} text chunks")
            
            # Show chunk content
            for i, (chunk_id, chunk_content) in enumerate(chunks_data.items()):
                if i < 2:
                    content_str = str(chunk_content)[:200]
                    logger.info(f"  • Chunk {i+1}: {content_str}...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector search test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🔍 Testing RAG with Document Context Retrieval")
    logger.info("=" * 60)
    
    # Test 1: Vector search directly
    vector_success = await test_vector_search_directly()
    
    # Test 2: RAG with context
    context_success = await test_rag_with_context()
    
    logger.info("\n" + "=" * 60)
    logger.info("🔍 Test Results:")
    logger.info(f"  Vector Search: {'✅ PASS' if vector_success else '❌ FAIL'}")
    logger.info(f"  RAG with Context: {'✅ PASS' if context_success else '❌ FAIL'}")
    
    if vector_success and context_success:
        logger.info("\n🎉 SUCCESS: RAG system is working with document context!")
        logger.info("✅ Vector embeddings: Functional")
        logger.info("✅ Knowledge graph: Functional") 
        logger.info("✅ Document retrieval: Working")
        logger.info("✅ LLM integration: Working")
        logger.info("✅ End-to-end RAG pipeline: Complete")
    else:
        logger.info("\n⚠️ Some issues detected - check responses for document context")

if __name__ == "__main__":
    asyncio.run(main())
