"""
Fixed LightRAG integration with AWS Bedrock
Addresses environment variable and inference profile issues
"""
import os
import asyncio
import logging
from pathlib import Path
import boto3
import json
import numpy as np
from lightrag import LightRAG, QueryParam
from lightrag.utils import EmbeddingFunc
from lightrag.kg.shared_storage import initialize_pipeline_status
import nest_asyncio

from config import (
    LLM_CONFIG, EMBEDDING_CONFIG, WORKING_DIR, 
    AWS_PROFILE, AWS_REGION, LLM_MODEL, EMBEDDING_MODEL
)
from document_processor import DocxProcessor

# Apply nest_asyncio for Jupyter/async compatibility
nest_asyncio.apply()

logger = logging.getLogger(__name__)

class FixedLightRAGBedrock:
    """Fixed LightRAG implementation using AWS Bedrock models"""
    
    def __init__(self, working_dir: Path = WORKING_DIR):
        self.working_dir = working_dir
        self.working_dir.mkdir(exist_ok=True)
        self.rag = None
        self.logger = logging.getLogger(__name__)
        
        # Set AWS environment for LightRAG Bedrock integration
        self._setup_aws_environment()
    
    def _setup_aws_environment(self):
        """Setup AWS environment variables for Bedrock"""
        # LightRAG Bedrock integration uses these environment variables
        os.environ["AWS_PROFILE"] = AWS_PROFILE
        os.environ["AWS_DEFAULT_REGION"] = AWS_REGION
        
        # Set AWS credentials to empty strings to avoid None type errors
        # The profile will be used for authentication
        os.environ["AWS_ACCESS_KEY_ID"] = ""
        os.environ["AWS_SECRET_ACCESS_KEY"] = ""
        os.environ["AWS_SESSION_TOKEN"] = ""
        
        self.logger.info(f"AWS environment configured: Profile={AWS_PROFILE}, Region={AWS_REGION}")
    
    async def _fixed_bedrock_complete(self, prompt, **kwargs):
        """Fixed Bedrock LLM function that handles inference profiles properly"""
        try:
            # Remove conflicting parameters
            kwargs.pop('model', None)
            kwargs.pop('hashing_kv', None)
            
            # Create Bedrock client using profile
            session = boto3.Session(profile_name=AWS_PROFILE)
            client = session.client('bedrock-runtime', region_name=AWS_REGION)
            
            # Prepare request for Nova Micro (using messages format)
            request_body = {
                "messages": [
                    {
                        "role": "user",
                        "content": [{"text": prompt}]
                    }
                ],
                "inferenceConfig": {
                    "maxTokens": kwargs.get("max_tokens", 4000),
                    "temperature": kwargs.get("temperature", 0.1),
                    "topP": kwargs.get("top_p", 0.9)
                }
            }
            
            # Use inference profile ID for Nova Micro
            response = client.invoke_model(
                modelId=LLM_MODEL,  # This is the inference profile ID
                body=json.dumps(request_body),
                contentType="application/json",
                accept="application/json"
            )
            
            response_body = json.loads(response['body'].read())
            # Nova models return content in a different format
            output = response_body.get('output', {})
            message = output.get('message', {})
            content = message.get('content', [])
            if content and len(content) > 0:
                return content[0].get('text', '')
            return ''
            
        except Exception as e:
            self.logger.error(f"Fixed Bedrock LLM call failed: {e}")
            raise
    
    async def _fixed_bedrock_embed(self, texts):
        """Fixed Bedrock embedding function"""
        try:
            # Create Bedrock client using profile
            session = boto3.Session(profile_name=AWS_PROFILE)
            client = session.client('bedrock-runtime', region_name=AWS_REGION)
            
            # Handle single text or list of texts
            if isinstance(texts, str):
                texts = [texts]
            
            embeddings = []
            for text in texts:
                request_body = {
                    "inputText": text
                }
                
                response = client.invoke_model(
                    modelId=EMBEDDING_MODEL,  # Direct model ID for Titan
                    body=json.dumps(request_body),
                    contentType="application/json",
                    accept="application/json"
                )
                
                response_body = json.loads(response['body'].read())
                embedding = response_body.get('embedding', [])
                embeddings.append(embedding)
            
            # Convert to numpy array format expected by LightRAG
            return np.array(embeddings)
            
        except Exception as e:
            self.logger.error(f"Fixed Bedrock embedding call failed: {e}")
            raise
    
    async def initialize(self):
        """Initialize LightRAG with fixed Bedrock models"""
        try:
            self.logger.info("Initializing LightRAG with fixed Bedrock models...")
            
            # Create LightRAG instance with fixed Bedrock functions
            self.rag = LightRAG(
                working_dir=str(self.working_dir),
                llm_model_func=self._fixed_bedrock_complete,
                embedding_func=EmbeddingFunc(
                    embedding_dim=EMBEDDING_CONFIG["dimensions"],
                    max_token_size=8192,
                    func=self._fixed_bedrock_embed
                )
            )
            
            # Initialize storages and pipeline
            await self.rag.initialize_storages()
            await initialize_pipeline_status()
            
            self.logger.info("LightRAG initialized successfully with fixed Bedrock")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize LightRAG: {e}")
            return False
    
    async def ingest_documents(self, docs_path: str):
        """Ingest .docx documents into LightRAG"""
        try:
            if not self.rag:
                raise Exception("LightRAG not initialized. Call initialize() first.")
            
            self.logger.info(f"Starting document ingestion from {docs_path}")
            
            # Process documents
            processor = DocxProcessor(docs_path)
            combined_content = processor.get_combined_content()
            
            if not combined_content.strip():
                raise Exception("No content found in documents")
            
            # Insert content into LightRAG
            await self.rag.ainsert(combined_content)
            
            self.logger.info("Document ingestion completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Document ingestion failed: {e}")
            return False
    
    async def query(self, question: str, mode: str = "naive") -> str:
        """Query the RAG system using naive mode (simpler, more reliable)"""
        try:
            if not self.rag:
                raise Exception("LightRAG not initialized. Call initialize() first.")
            
            self.logger.info(f"Processing query: {question} (mode: {mode})")
            
            # Execute query with specified mode
            response = await self.rag.aquery(
                question, 
                param=QueryParam(mode=mode)
            )
            
            self.logger.info("Query processed successfully")
            return response
            
        except Exception as e:
            self.logger.error(f"Query failed: {e}")
            return f"Error processing query: {e}"
