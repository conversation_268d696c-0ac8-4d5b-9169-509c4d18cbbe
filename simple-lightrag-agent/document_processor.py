"""
Document processor for .docx files
"""
import logging
from pathlib import Path
from typing import List, Dict, Any
from docx import Document

logger = logging.getLogger(__name__)

class DocxProcessor:
    """Processor for .docx documents"""
    
    def __init__(self, docs_path: str):
        self.docs_path = Path(docs_path)
        self.logger = logging.getLogger(__name__)
    
    def find_docx_files(self) -> List[Path]:
        """Find all .docx files in the docs directory"""
        try:
            docx_files = list(self.docs_path.glob("*.docx"))
            self.logger.info(f"Found {len(docx_files)} .docx files in {self.docs_path}")
            return docx_files
        except Exception as e:
            self.logger.error(f"Error finding .docx files: {e}")
            return []
    
    def extract_text_from_docx(self, file_path: Path) -> str:
        """Extract text content from a .docx file"""
        try:
            doc = Document(file_path)
            content = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            content.append(cell.text.strip())
            
            text_content = "\n".join(content)
            self.logger.info(f"Extracted {len(text_content)} characters from {file_path.name}")
            return text_content
            
        except Exception as e:
            self.logger.error(f"Error extracting text from {file_path}: {e}")
            return ""
    
    def process_all_documents(self) -> List[Dict[str, Any]]:
        """Process all .docx documents and return structured data"""
        documents = []
        docx_files = self.find_docx_files()
        
        for file_path in docx_files:
            content = self.extract_text_from_docx(file_path)
            if content:
                doc_data = {
                    "filename": file_path.name,
                    "filepath": str(file_path),
                    "content": content,
                    "size": len(content)
                }
                documents.append(doc_data)
                self.logger.info(f"Processed document: {file_path.name}")
        
        self.logger.info(f"Successfully processed {len(documents)} documents")
        return documents
    
    def get_combined_content(self) -> str:
        """Get all document content combined into a single string"""
        documents = self.process_all_documents()
        combined_content = []
        
        for doc in documents:
            combined_content.append(f"=== {doc['filename']} ===")
            combined_content.append(doc['content'])
            combined_content.append("")  # Empty line separator
        
        return "\n".join(combined_content)
